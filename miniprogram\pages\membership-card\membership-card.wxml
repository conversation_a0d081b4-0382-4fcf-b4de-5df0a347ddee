<!--
  membership-card.wxml - 考勤卡页面结构文件

  页面功能：展示用户的考勤卡信息
  设计风格：现代化卡片设计，注重视觉层次和用户体验
-->
<view class="membership-card-page" data-loading="{{loading}}">
  <!-- 考勤卡列表容器 - 当有数据时显示 -->
  <block wx:if="{{!loading && cards.length > 0}}">
    <view class="card-list">
      <!--
        考勤卡片循环渲染
        wx:for: 遍历cards数组，每个元素赋值给item
        wx:key: 使用_id作为唯一标识，提升渲染性能
      -->
      <view class="membership-card"
            wx:for="{{cards}}"
            wx:key="_id"
            data-index="{{index}}"
            data-status="{{item.isExpired ? 'expired' : (item.isExpiring ? 'expiring' : 'valid')}}">
        <!-- 卡片装饰元素 - 右上角装饰圆形 -->
        <view class="card-decoration"></view>

        <!-- 卡片磁条效果 - 中间的装饰条纹 -->
        <view class="card-stripe"></view>

        <!-- 卡片品牌标识 -->
        <view class="card-brand">考勤卡</view>

        <!--
          卡片头部区域 - 模拟真实卡片的芯片和状态区域
          包含：芯片图标、状态标签
        -->
        <view class="card-header">
          <!--
            卡片芯片 - 模拟银行卡芯片
            使用creditcard图标模拟芯片外观
          -->
          <t-icon name="creditcard" size="24" />

          <!--
            状态标签 - 卡片右上角状态指示
            使用毛玻璃效果，模拟现代卡片设计
          -->
          <t-tag wx:if="{{item.isExpired}}" theme="danger" size="small">已过期</t-tag>
          <t-tag wx:elif="{{item.isExpiring}}" theme="warning" size="small">即将到期</t-tag>
          <t-tag wx:else theme="success" size="small">有效</t-tag>
        </view>

        <!--
          卡号显示区域 - 模拟真实卡片的卡号位置
          位于卡片底部，使用等宽字体
        -->
        <view class="card-number">{{item.cardNumber}}</view>

        <!--
          卡片主体内容区域
          包含：有效期、总次数、剩余次数、颁发日期
        -->
        <view class="card-body">
          <!--
            信息行 - 有效期
            使用flex布局，左侧标签，右侧数值
          -->
          <view class="card-row validity-row">
            <text class="label">有效期</text>
            <text class="value">{{item.validFrom}} ~ {{item.validTo}}</text>
          </view>

          <!-- 信息行 - 总次数 -->
          <view class="card-row total-times-row">
            <text class="label">总次数</text>
            <text class="value">{{item.totalTimes}}</text>
          </view>

          <!-- 信息行 - 剩余次数（重要信息，突出显示） -->
          <view class="card-row remaining-times-row">
            <text class="label">剩余次数</text>
            <text class="value highlight">{{item.remainingTimes}}</text>
          </view>

          <!-- 信息行 - 颁发日期 -->
          <view class="card-row issue-date-row">
            <text class="label">颁发日期</text>
            <text class="value">{{item.issueDate}}</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!--
    空状态组件 - 当没有考勤卡时显示
    wx:if: 条件渲染，当不在加载且卡片数量为0时显示
    description: 空状态描述文字
  -->
  <t-empty wx:if="{{!loading && cards.length === 0}}"
           description="暂无考勤卡"
           icon="creditcard" />

  <!-- TabBar占位符 - 预留底部导航栏空间 -->
  <view id="tab-bar-placeholder"></view>

  <!-- Toast消息提示组件 -->
  <t-toast id="t-toast" />
</view>