/* 页面容器样式 - 简洁的单色背景 */
.membership-card-page {
  min-height: 100vh;
  /* 使用简洁的浅灰色背景 */
  background: #f8f9fa;
  padding-bottom: 120rpx; /* 预留底部TabBar空间 */
  padding-top: 20rpx; /* 顶部留白 */
}

/* 卡片列表容器 - 优化间距和布局 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx; /* 增加卡片间距，提升视觉层次 */
  margin: 0 32rpx; /* 左右边距统一使用rpx单位 */
  padding-top: 20rpx; /* 顶部内边距 */
}

/* 考勤卡片样式 - 真实卡片设计 */
.membership-card {
  /* 卡片尺寸：模拟真实银行卡比例 (85.60 × 53.98 mm) */
  width: calc(100vw - 64rpx); /* 响应式宽度 */
  max-width: 640rpx; /* 最大宽度限制 */
  height: 400rpx; /* 固定高度，保持卡片比例 */

  /* 卡片背景：渐变色模拟塑料卡片质感 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  /* 卡片圆角：真实卡片的圆角 */
  border-radius: 24rpx;

  /* 卡片阴影：模拟真实卡片的立体感 */
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.3),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);

  padding: 40rpx 32rpx;
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /* 添加过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 卡片边框：微妙的边框增强质感 */
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  /* 添加卡片纹理效果 */
  overflow: hidden;
}

/* 卡片背景纹理 - 模拟真实卡片的微妙纹理 */
.membership-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 卡片光泽效果 - 模拟塑料卡片的反光 */
.membership-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  transform: rotate(45deg);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}
/* 卡片装饰元素 - 模拟真实卡片的装饰图案 */
.membership-card .card-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
  pointer-events: none;
}

/* 卡片品牌标识区域 */
.membership-card .card-brand {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
  letter-spacing: 1rpx;
  z-index: 2;
}

/* 卡片磁条效果（装饰性） */
.membership-card .card-stripe {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 60rpx;
  background: linear-gradient(90deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.3) 100%);
  transform: translateY(-50%);
  opacity: 0.3;
  pointer-events: none;
}
/* 不同状态卡片的渐变色 */
.membership-card[data-status="expired"] {
  background: linear-gradient(135deg, #8b5a5a 0%, #6b4444 100%);
  box-shadow:
    0 8rpx 32rpx rgba(139, 90, 90, 0.3),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

.membership-card[data-status="expiring"] {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  box-shadow:
    0 8rpx 32rpx rgba(217, 119, 6, 0.3),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

.membership-card[data-status="valid"] {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow:
    0 8rpx 32rpx rgba(5, 150, 105, 0.3),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

/* 默认渐变（有效卡片） */
.membership-card:not([data-status]) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 卡片悬停效果 - 显示光泽 */
.membership-card:hover::after {
  opacity: 1;
}

/* 卡片点击效果 - 真实卡片的按压感 */
.membership-card:active {
  transform: scale(0.98) translateY(2rpx) rotateX(2deg);
  box-shadow:
    0 4rpx 16rpx rgba(102, 126, 234, 0.2),
    0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 卡片3D透视效果 */
.card-list {
  perspective: 1000rpx;
}

.membership-card {
  transform-style: preserve-3d;
}

/* 卡片头部样式 - 真实卡片的头部设计 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 两端对齐 */
  margin-bottom: 40rpx;
  position: relative;
  z-index: 2; /* 确保在纹理之上 */
}

/* 卡片图标样式 - 模拟卡片芯片 */
.card-header t-icon {
  /* 模拟银行卡芯片的样式 */
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 12rpx;
  border-radius: 8rpx;
  box-shadow:
    inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1),
    0 1rpx 2rpx rgba(255, 255, 255, 0.5);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  margin-right: 0; /* 重置边距 */
}

/* 卡号样式 - 模拟真实卡片的卡号 */
.card-number {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95); /* 白色文字 */
  font-family: 'Courier New', monospace; /* 等宽字体，模拟卡片数字 */
  letter-spacing: 2rpx; /* 字母间距 */
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3); /* 文字阴影增强可读性 */
  position: absolute;
  bottom: 80rpx; /* 定位到卡片底部 */
  left: 32rpx;
  z-index: 2;
}

/* 状态标签样式 - 卡片右上角状态指示 */
.card-header t-tag {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 8rpx;
  flex-shrink: 0;
  white-space: nowrap;
  padding: 6rpx 12rpx !important;
  background: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx); /* 毛玻璃效果 */
}

/* 卡片主体内容样式 - 卡片背面信息区域 */
.card-body {
  position: absolute;
  bottom: 32rpx;
  left: 32rpx;
  right: 32rpx;
  z-index: 2;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx); /* 毛玻璃效果 */
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 信息行样式 - 卡片信息文字 */
.card-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
  padding: 4rpx 0;
  transition: all 0.2s ease;
}

/* 最后一行不显示边距 */
.card-row:last-child {
  margin-bottom: 0;
}

/* 标签文字样式 - 卡片信息标签 */
.card-row text:first-child,
.card-row .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  font-size: 22rpx;
  min-width: 120rpx;
}

/* 数值文字样式 - 卡片信息数值 */
.card-row text:last-child,
.card-row .value {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
  font-size: 24rpx;
  text-align: right;
  flex: 1;
}

/* 空状态样式美化 */
.membership-card-page t-empty {
  margin-top: 120rpx; /* 增加顶部间距 */
  /* 添加背景装饰 */
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-left: 32rpx;
  margin-right: 32rpx;
  /* 添加阴影 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  /* 添加边框 */
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

/* TabBar占位符样式 */
#tab-bar-placeholder {
  height: 120rpx; /* 确保底部留白 */
}

/* 响应式设计 - 针对不同屏幕尺寸优化 */
@media screen and (max-width: 375px) {
  .card-list {
    margin: 0 24rpx; /* 小屏幕减少边距 */
  }

  .membership-card {
    padding: 24rpx 20rpx; /* 小屏幕减少内边距 */
  }

  .card-number {
    font-size: 32rpx; /* 小屏幕减小字体 */
  }
}

@media screen and (min-width: 768px) {
  .card-list {
    margin: 0 auto; /* 大屏幕居中显示 */
    max-width: 600rpx; /* 限制最大宽度 */
  }
}

/* 加载状态保持一致 */
.membership-card-page[data-loading="true"] {
  background: #f8f9fa;
}

/* 卡片进入动画 */
@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.membership-card {
  /* 添加进入动画 */
  animation: cardSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 为不同的卡片添加延迟动画 */
.membership-card:nth-child(1) { animation-delay: 0.1s; }
.membership-card:nth-child(2) { animation-delay: 0.2s; }
.membership-card:nth-child(3) { animation-delay: 0.3s; }
.membership-card:nth-child(4) { animation-delay: 0.4s; }
.membership-card:nth-child(5) { animation-delay: 0.5s; }

/* 状态标签保持TDesign默认样式 - 简洁统一 */

/* 简化微交互效果 */
.card-row:active {
  opacity: 0.7;
}

/* 优化文字选择效果 */
.card-number::selection,
.card-row text::selection {
  background: rgba(59, 130, 246, 0.2);
  color: inherit;
}

/* 新增的语义化样式类 */

/* 标签文字统一样式 */
.card-row .label {
  font-weight: 600;
  color: #6b7280;
  font-size: 26rpx;
}

/* 数值文字统一样式 */
.card-row .value {
  color: #1f2937;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

/* 重要数值高亮样式 - 卡片重点信息 */
.card-row .value.highlight {
  color: #fff;
  font-weight: 700;
  font-size: 28rpx;
  text-shadow: 0 0 8rpx rgba(255, 255, 255, 0.5);
}

/* 移除特定信息行的个性化颜色样式 - 保持统一 */

/* 卡片索引相关的动画延迟优化 */
.membership-card[data-index="0"] { animation-delay: 0.1s; }
.membership-card[data-index="1"] { animation-delay: 0.2s; }
.membership-card[data-index="2"] { animation-delay: 0.3s; }
.membership-card[data-index="3"] { animation-delay: 0.4s; }
.membership-card[data-index="4"] { animation-delay: 0.5s; }
.membership-card[data-index="5"] { animation-delay: 0.6s; }

/* 深色模式支持（简化版） */
@media (prefers-color-scheme: dark) {
  .membership-card-page {
    background: #1f2937;
  }

  .membership-card {
    background: #374151;
    border-color: rgba(255, 255, 255, 0.1);
  }

  .card-number {
    color: #f9fafb;
  }

  .card-row {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .card-row .label {
    color: #d1d5db;
  }

  .card-row .value {
    color: #f3f4f6;
  }
}