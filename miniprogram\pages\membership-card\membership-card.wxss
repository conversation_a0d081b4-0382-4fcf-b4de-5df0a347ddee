/* 页面容器样式 - 采用渐变背景增强视觉效果 */
.membership-card-page {
  min-height: 100vh;
  /* 使用线性渐变背景，从浅蓝到浅灰，营造现代感 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 120rpx; /* 预留底部TabBar空间 */
  padding-top: 20rpx; /* 顶部留白 */
}

/* 卡片列表容器 - 优化间距和布局 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx; /* 增加卡片间距，提升视觉层次 */
  margin: 0 32rpx; /* 左右边距统一使用rpx单位 */
  padding-top: 20rpx; /* 顶部内边距 */
}

/* 考勤卡片样式 - 现代化卡片设计 */
.membership-card {
  background: #fff;
  /* 增大圆角，更现代的设计风格 */
  border-radius: 20rpx;
  /* 增强阴影效果，提升层次感 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  padding: 32rpx 28rpx 28rpx 28rpx; /* 增加内边距，提升内容舒适度 */
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  /* 添加过渡动画，提升交互体验 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 添加边框，增强卡片边界 */
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  /* 添加背景渐变，增加视觉深度 */
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
}

/* 卡片悬停效果 - 增强交互反馈 */
.membership-card:active {
  /* 点击时轻微缩放和阴影变化 */
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12), 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

/* 卡片头部样式 - 优化布局和视觉层次 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx; /* 增加底部间距 */
  padding-bottom: 16rpx; /* 添加底部内边距 */
  /* 添加底部分割线，增强视觉分层 */
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  position: relative;
}

/* 卡片图标样式 - 增强视觉吸引力 */
.card-header t-icon {
  margin-right: 16rpx; /* 增加右边距 */
  /* 使用渐变色，增强视觉效果 */
  color: #0052d9;
  /* 添加背景圆形，突出图标 */
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 50%;
  padding: 8rpx;
  /* 添加轻微阴影 */
  box-shadow: 0 2rpx 8rpx rgba(0, 82, 217, 0.15);
}

/* 卡号样式 - 增强可读性和视觉重点 */
.card-number {
  font-size: 36rpx; /* 增大字体 */
  font-weight: 700; /* 加粗字体 */
  color: #1a1a1a; /* 更深的颜色，增强对比度 */
  margin-right: 16rpx; /* 增加右边距 */
  flex: 1; /* 占据剩余空间 */
  /* 添加文字阴影，增强立体感 */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 状态标签样式 - 现代化标签设计 */
.card-header t-tag {
  margin-left: auto; /* 自动左边距，推到右侧 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 22rpx; /* 调整字体大小 */
  font-weight: 600; /* 加粗字体 */
  border-radius: 12rpx; /* 增大圆角 */
  flex-shrink: 0;
  white-space: nowrap;
  /* 添加内边距，增强视觉效果 */
  padding: 8rpx 16rpx !important;
  /* 添加轻微阴影 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  /* 添加过渡动画 */
  transition: all 0.2s ease;
}

/* 卡片主体内容样式 - 优化信息展示 */
.card-body {
  display: flex;
  flex-direction: column;
  gap: 16rpx; /* 增加行间距，提升可读性 */
}

/* 信息行样式 - 现代化信息展示 */
.card-row {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  font-size: 28rpx; /* 增大字体，提升可读性 */
  color: #4a4a4a; /* 调整颜色，增强对比度 */
  margin-bottom: 4rpx; /* 轻微底部间距 */
  padding: 12rpx 16rpx; /* 添加内边距 */
  /* 添加背景色，增强视觉层次 */
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12rpx; /* 圆角设计 */
  /* 添加边框，增强边界感 */
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  /* 添加过渡动画 */
  transition: all 0.2s ease;
  position: relative;
}

/* 信息行悬停效果 */
.card-row:hover {
  background: rgba(248, 250, 252, 0.9);
  transform: translateX(4rpx); /* 轻微右移 */
}

/* 标签文字样式 - 增强标签的视觉重点 */
.card-row text:first-child {
  min-width: 140rpx; /* 增加最小宽度，保持对齐 */
  color: #6b7280; /* 调整颜色 */
  font-weight: 600; /* 加粗字体 */
  font-size: 26rpx; /* 稍小的字体 */
  /* 添加右边距分隔符效果 */
  position: relative;
}

/* 为标签添加装饰性分隔符 */
.card-row text:first-child::after {
  content: '';
  position: absolute;
  right: -8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
  height: 20rpx;
  background: linear-gradient(to bottom, #e5e7eb, #d1d5db);
  border-radius: 1rpx;
}

/* 数值文字样式 - 突出重要信息 */
.card-row text:last-child {
  color: #1f2937; /* 更深的颜色 */
  font-weight: 500; /* 中等粗细 */
  flex: 1; /* 占据剩余空间 */
  text-align: right; /* 右对齐 */
}

/* 特殊信息行样式 - 为重要信息添加特殊样式 */
.card-row:first-child {
  /* 有效期行使用特殊背景 */
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.08) 100%);
  border-color: rgba(59, 130, 246, 0.1);
}

.card-row:nth-child(3) {
  /* 剩余次数行使用特殊背景 */
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(110, 231, 183, 0.08) 100%);
  border-color: rgba(16, 185, 129, 0.1);
}

/* 空状态样式美化 */
.membership-card-page t-empty {
  margin-top: 120rpx; /* 增加顶部间距 */
  /* 添加背景装饰 */
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-left: 32rpx;
  margin-right: 32rpx;
  /* 添加阴影 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  /* 添加边框 */
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

/* TabBar占位符样式 */
#tab-bar-placeholder {
  height: 120rpx; /* 确保底部留白 */
}

/* 响应式设计 - 针对不同屏幕尺寸优化 */
@media screen and (max-width: 375px) {
  .card-list {
    margin: 0 24rpx; /* 小屏幕减少边距 */
  }

  .membership-card {
    padding: 24rpx 20rpx; /* 小屏幕减少内边距 */
  }

  .card-number {
    font-size: 32rpx; /* 小屏幕减小字体 */
  }
}

@media screen and (min-width: 768px) {
  .card-list {
    margin: 0 auto; /* 大屏幕居中显示 */
    max-width: 600rpx; /* 限制最大宽度 */
  }
}

/* 加载状态优化 */
.membership-card-page[data-loading="true"] {
  /* 加载时的背景效果 */
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 卡片进入动画 */
@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.membership-card {
  /* 添加进入动画 */
  animation: cardSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 为不同的卡片添加延迟动画 */
.membership-card:nth-child(1) { animation-delay: 0.1s; }
.membership-card:nth-child(2) { animation-delay: 0.2s; }
.membership-card:nth-child(3) { animation-delay: 0.3s; }
.membership-card:nth-child(4) { animation-delay: 0.4s; }
.membership-card:nth-child(5) { animation-delay: 0.5s; }

/* 状态标签特殊样式 */
.card-header t-tag[theme="success"] {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
  color: white !important;
}

.card-header t-tag[theme="warning"] {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
  color: white !important;
}

.card-header t-tag[theme="danger"] {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%) !important;
  color: white !important;
}

/* 微交互效果 */
.card-row:active {
  transform: translateX(2rpx) scale(0.98);
  background: rgba(248, 250, 252, 1);
}

/* 优化文字选择效果 */
.card-number::selection,
.card-row text::selection {
  background: rgba(59, 130, 246, 0.2);
  color: inherit;
}

/* 新增的语义化样式类 */

/* 标签文字统一样式 */
.card-row .label {
  font-weight: 600;
  color: #6b7280;
  font-size: 26rpx;
}

/* 数值文字统一样式 */
.card-row .value {
  color: #1f2937;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

/* 重要数值高亮样式 */
.card-row .value.highlight {
  color: #059669; /* 绿色突出显示 */
  font-weight: 700;
  font-size: 30rpx;
  /* 添加轻微的发光效果 */
  text-shadow: 0 0 8rpx rgba(5, 150, 105, 0.3);
}

/* 特定信息行的个性化样式 */
.validity-row {
  /* 有效期行 - 蓝色主题 */
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.08) 100%);
  border-color: rgba(59, 130, 246, 0.1);
}

.total-times-row {
  /* 总次数行 - 紫色主题 */
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(196, 181, 253, 0.08) 100%);
  border-color: rgba(139, 92, 246, 0.1);
}

.remaining-times-row {
  /* 剩余次数行 - 绿色主题，重点突出 */
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, rgba(110, 231, 183, 0.12) 100%);
  border-color: rgba(16, 185, 129, 0.15);
  /* 添加左侧装饰条 */
  border-left: 4rpx solid #10b981;
}

.issue-date-row {
  /* 颁发日期行 - 灰色主题 */
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.06) 0%, rgba(156, 163, 175, 0.06) 100%);
  border-color: rgba(107, 114, 128, 0.08);
}

/* 卡片索引相关的动画延迟优化 */
.membership-card[data-index="0"] { animation-delay: 0.1s; }
.membership-card[data-index="1"] { animation-delay: 0.2s; }
.membership-card[data-index="2"] { animation-delay: 0.3s; }
.membership-card[data-index="3"] { animation-delay: 0.4s; }
.membership-card[data-index="4"] { animation-delay: 0.5s; }
.membership-card[data-index="5"] { animation-delay: 0.6s; }

/* 深色模式支持（可选） */
@media (prefers-color-scheme: dark) {
  .membership-card-page {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  .membership-card {
    background: linear-gradient(145deg, #374151 0%, #4b5563 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .card-number {
    color: #f9fafb;
  }

  .card-row {
    background: rgba(55, 65, 81, 0.6);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .card-row .label {
    color: #d1d5db;
  }

  .card-row .value {
    color: #f3f4f6;
  }
}