/* 页面容器样式 - 简洁的单色背景 */
.membership-card-page {
  min-height: 100vh;
  /* 使用简洁的浅灰色背景 */
  background: #f8f9fa;
  padding-bottom: 120rpx; /* 预留底部TabBar空间 */
  padding-top: 20rpx; /* 顶部留白 */
}

/* 卡片列表容器 - 优化间距和布局 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx; /* 增加卡片间距，提升视觉层次 */
  margin: 0 32rpx; /* 左右边距统一使用rpx单位 */
  padding-top: 20rpx; /* 顶部内边距 */
}

/* 考勤卡片样式 - 简洁的卡片设计 */
.membership-card {
  background: #fff;
  /* 适中的圆角设计 */
  border-radius: 16rpx;
  /* 简洁的阴影效果 */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  padding: 32rpx 28rpx 28rpx 28rpx;
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  /* 添加过渡动画，提升交互体验 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 添加边框，增强卡片边界 */
  border: 1rpx solid rgba(0, 0, 0, 0.06);
}

/* 卡片点击效果 - 简洁的交互反馈 */
.membership-card:active {
  /* 点击时轻微缩放 */
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/* 卡片头部样式 - 优化布局和视觉层次 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx; /* 增加底部间距 */
  padding-bottom: 16rpx; /* 添加底部内边距 */
  /* 添加底部分割线，增强视觉分层 */
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  position: relative;
}

/* 卡片图标样式 - 简洁设计 */
.card-header t-icon {
  margin-right: 16rpx;
  color: #0052d9;
}

/* 卡号样式 - 增强可读性和视觉重点 */
.card-number {
  font-size: 36rpx; /* 增大字体 */
  font-weight: 700; /* 加粗字体 */
  color: #1a1a1a; /* 更深的颜色，增强对比度 */
  margin-right: 16rpx; /* 增加右边距 */
  flex: 1; /* 占据剩余空间 */
  /* 添加文字阴影，增强立体感 */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 状态标签样式 - 现代化标签设计 */
.card-header t-tag {
  margin-left: auto; /* 自动左边距，推到右侧 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 22rpx; /* 调整字体大小 */
  font-weight: 600; /* 加粗字体 */
  border-radius: 12rpx; /* 增大圆角 */
  flex-shrink: 0;
  white-space: nowrap;
  /* 添加内边距，增强视觉效果 */
  padding: 8rpx 16rpx !important;
  /* 添加轻微阴影 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  /* 添加过渡动画 */
  transition: all 0.2s ease;
}

/* 卡片主体内容样式 - 优化信息展示 */
.card-body {
  display: flex;
  flex-direction: column;
  gap: 16rpx; /* 增加行间距，提升可读性 */
}

/* 信息行样式 - 简洁的信息展示 */
.card-row {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #4a4a4a;
  margin-bottom: 8rpx;
  padding: 12rpx 0;
  /* 简洁的底部边框分隔 */
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  /* 添加过渡动画 */
  transition: all 0.2s ease;
}

/* 最后一行不显示边框 */
.card-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* 标签文字样式 - 简洁的标签设计 */
.card-row text:first-child {
  min-width: 140rpx;
  color: #6b7280;
  font-weight: 600;
  font-size: 26rpx;
}

/* 数值文字样式 - 突出重要信息 */
.card-row text:last-child {
  color: #1f2937;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

/* 空状态样式美化 */
.membership-card-page t-empty {
  margin-top: 120rpx; /* 增加顶部间距 */
  /* 添加背景装饰 */
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-left: 32rpx;
  margin-right: 32rpx;
  /* 添加阴影 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  /* 添加边框 */
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

/* TabBar占位符样式 */
#tab-bar-placeholder {
  height: 120rpx; /* 确保底部留白 */
}

/* 响应式设计 - 针对不同屏幕尺寸优化 */
@media screen and (max-width: 375px) {
  .card-list {
    margin: 0 24rpx; /* 小屏幕减少边距 */
  }

  .membership-card {
    padding: 24rpx 20rpx; /* 小屏幕减少内边距 */
  }

  .card-number {
    font-size: 32rpx; /* 小屏幕减小字体 */
  }
}

@media screen and (min-width: 768px) {
  .card-list {
    margin: 0 auto; /* 大屏幕居中显示 */
    max-width: 600rpx; /* 限制最大宽度 */
  }
}

/* 加载状态保持一致 */
.membership-card-page[data-loading="true"] {
  background: #f8f9fa;
}

/* 卡片进入动画 */
@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.membership-card {
  /* 添加进入动画 */
  animation: cardSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 为不同的卡片添加延迟动画 */
.membership-card:nth-child(1) { animation-delay: 0.1s; }
.membership-card:nth-child(2) { animation-delay: 0.2s; }
.membership-card:nth-child(3) { animation-delay: 0.3s; }
.membership-card:nth-child(4) { animation-delay: 0.4s; }
.membership-card:nth-child(5) { animation-delay: 0.5s; }

/* 状态标签保持TDesign默认样式 - 简洁统一 */

/* 简化微交互效果 */
.card-row:active {
  opacity: 0.7;
}

/* 优化文字选择效果 */
.card-number::selection,
.card-row text::selection {
  background: rgba(59, 130, 246, 0.2);
  color: inherit;
}

/* 新增的语义化样式类 */

/* 标签文字统一样式 */
.card-row .label {
  font-weight: 600;
  color: #6b7280;
  font-size: 26rpx;
}

/* 数值文字统一样式 */
.card-row .value {
  color: #1f2937;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

/* 重要数值高亮样式 - 简化设计 */
.card-row .value.highlight {
  color: #0052d9; /* 使用主题蓝色 */
  font-weight: 700;
  font-size: 30rpx;
}

/* 移除特定信息行的个性化颜色样式 - 保持统一 */

/* 卡片索引相关的动画延迟优化 */
.membership-card[data-index="0"] { animation-delay: 0.1s; }
.membership-card[data-index="1"] { animation-delay: 0.2s; }
.membership-card[data-index="2"] { animation-delay: 0.3s; }
.membership-card[data-index="3"] { animation-delay: 0.4s; }
.membership-card[data-index="4"] { animation-delay: 0.5s; }
.membership-card[data-index="5"] { animation-delay: 0.6s; }

/* 深色模式支持（简化版） */
@media (prefers-color-scheme: dark) {
  .membership-card-page {
    background: #1f2937;
  }

  .membership-card {
    background: #374151;
    border-color: rgba(255, 255, 255, 0.1);
  }

  .card-number {
    color: #f9fafb;
  }

  .card-row {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .card-row .label {
    color: #d1d5db;
  }

  .card-row .value {
    color: #f3f4f6;
  }
}